"use client";
import { use<PERSON>et<PERSON><PERSON> } from "jotai";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { PdfService } from "../../services/pdf-service";
import { pdfDocumentProxy } from "../../states/pdf-proxy.state";

interface LoadPdfProxyProps {
	id: string;
	buffer: ArrayBuffer;
	service?: PdfService;
	onLoadStart?: () => void;
	onLoadEnd?: () => void;
	onError?: (error: string) => void;
}

interface ILoadPdfProxyReturn {
	clearCache: () => void;
	isLoading: boolean;
	error: string | null;
	retry: () => void;
}

const PDF_VALIDATION_ERRORS = {
	EMPTY_BUFFER: "Buffer de PDF vazio ou inválido",
	INVALID_PDF: "O arquivo não é um documento PDF válido",
	CORRUPTED_PDF: "Estrutura do PDF é inválida ou arquivo corrompido",
	PROCESSING_FAILED: "Não foi possível processar o documento PDF",
	UNKNOWN: "Erro desconhecido ao carregar o PDF",
} as const;

export const useLoadPdfProxy = ({ id, buffer, service, onLoadStart, onLoadEnd, onError }: LoadPdfProxyProps): ILoadPdfProxyReturn => {
	const setDocumentProxy = useSetAtom(pdfDocumentProxy);
	const pdfService = useMemo(() => service || new PdfService(), [service]);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const abortControllerRef = useRef<AbortController | null>(null);

	const validatePdfBuffer = useCallback((buffer: ArrayBuffer): void => {
		if (!buffer || buffer.byteLength === 0) {
			throw new Error(PDF_VALIDATION_ERRORS.EMPTY_BUFFER);
		}

		const uint8Array = new Uint8Array(buffer);
		const pdfHeader = String.fromCharCode(...uint8Array.slice(0, 4));

		if (pdfHeader !== "%PDF") {
			throw new Error(PDF_VALIDATION_ERRORS.INVALID_PDF);
		}
	}, []);

	const getErrorMessage = useCallback((error: unknown): string => {
		if (!(error instanceof Error)) {
			return PDF_VALIDATION_ERRORS.UNKNOWN;
		}

		const message = error.message.toLowerCase();

		if (message.includes("invalid pdf structure") || message.includes("invalidpdfexception")) {
			return PDF_VALIDATION_ERRORS.CORRUPTED_PDF;
		}

		if (message.includes("não é um pdf válido")) {
			return PDF_VALIDATION_ERRORS.INVALID_PDF;
		}

		if (message.includes("falha ao carregar o documento pdf")) {
			return PDF_VALIDATION_ERRORS.PROCESSING_FAILED;
		}

		return error.message || PDF_VALIDATION_ERRORS.UNKNOWN;
	}, []);

	const loadDocument = useCallback(async (): Promise<void> => {
		if (abortControllerRef.current) {
			abortControllerRef.current.abort();
		}

		abortControllerRef.current = new AbortController();

		try {
			if (window === undefined) return;

			validatePdfBuffer(buffer);

			setIsLoading(true);
			setError(null);
			onLoadStart?.();

			const pdf = await pdfService.getPdfDocumentProxy({ id, buffer });

			if (abortControllerRef.current.signal.aborted) {
				return;
			}

			if (!pdf) {
				throw new Error(PDF_VALIDATION_ERRORS.PROCESSING_FAILED);
			}

			setDocumentProxy(pdf);
		} catch (error) {
			if (abortControllerRef.current?.signal.aborted) {
				return;
			}

			const errorMessage = getErrorMessage(error);
			console.error("Erro ao carregar o PDF:", error);

			setError(errorMessage);
			setDocumentProxy(null);
			onError?.(errorMessage);
		} finally {
			if (!abortControllerRef.current?.signal.aborted) {
				setIsLoading(false);
				onLoadEnd?.();
			}
		}
	}, [id, buffer, pdfService, validatePdfBuffer, getErrorMessage, setDocumentProxy, onLoadStart, onLoadEnd, onError]);

	const retry = useCallback(() => {
		loadDocument();
	}, [loadDocument]);

	const clearCache = useCallback((): void => {
		pdfService.clearCache();
	}, [pdfService]);

	useEffect(() => {
		loadDocument();

		return () => {
			if (abortControllerRef.current) {
				abortControllerRef.current.abort();
			}
		};
	}, [loadDocument]);

	return {
		clearCache,
		isLoading,
		error,
		retry,
	};
};
