import { Stroke } from "../../hooks/signature/sign-page/signature-canvas.hook";

// Constantes de segurança para dimensões do SVG
const MAX_SVG_WIDTH = 400;
const MAX_SVG_HEIGHT = 400;

export const convertStrokesToSVG = (strokes: Stroke[], svgWidth: number, svgHeight: number): string => {
	// Validar e limitar dimensões do SVG para prevenir manipulação
	const safeWidth = Math.min(svgWidth, MAX_SVG_WIDTH);
	const safeHeight = Math.min(svgHeight, MAX_SVG_HEIGHT);

	const paths = strokes
		.map(stroke => {
			if (stroke.length === 0) return "";

			// Validar e limitar coordenadas dos pontos
			const validatedStroke = stroke.map(point => ({
				x: Math.max(0, Math.min(point.x, safeWidth)),
				y: Math.max(0, Math.min(point.y, safeHeight)),
			}));

			const d = validatedStroke.map((point, index) => (index === 0 ? `M ${point.x} ${point.y}` : `L ${point.x} ${point.y}`)).join(" ");
			return `<path d="${d}" fill="none" stroke="black" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />`;
		})
		.join("\n");

	return `<svg width="${safeWidth}" height="${safeHeight}" viewBox="0 0 ${safeWidth} ${safeHeight}" xmlns="http://www.w3.org/2000/svg">
  ${paths}
</svg>`;
};
